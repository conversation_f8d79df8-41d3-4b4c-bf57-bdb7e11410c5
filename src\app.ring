# ===================================================================
# Ring IDE Application Class
# ===================================================================

load "webview.ring"
load "jsonlib.ring"
load "stdlib.ring"
load "src/file_manager.ring"
load "src/code_runner.ring"
load "src/ai_assistant.ring"
load "src/ui_generator.ring"

# ===================================================================
# Main Application Class
# ===================================================================
class RingIDE
    
    # Public properties
    oWebView = NULL
    oFileManager = NULL
    oCodeRunner = NULL
    oAIAssistant = NULL
    oUIGenerator = NULL
    cCurrentProject = ""
    
    # ===================================================================
    # Constructor
    # ===================================================================
    func init()
        see "Initializing Ring IDE..." + nl
        
        # Initialize components
        oFileManager = new FileManager()
        oCodeRunner = new CodeRunner()
        oAIAssistant = new AIAssistant()
        oUIGenerator = new UIGenerator()
        
        see "Components initialized successfully." + nl
    
    # ===================================================================
    # Start Application
    # ===================================================================
    func start()
        try
            # Create webview instance
            oWebView = new WebView()
            
            # Configure webview
            oWebView {
                setTitle("Ring Programming IDE - AI Assistant")
                setSize(1400, 900, WEBVIEW_HINT_NONE)
                
                # Bind Ring functions to JavaScript
                bind("saveFile", :saveFile)
                bind("loadFile", :loadFile)
                bind("runCode", :runCode)
                bind("chatWithAI", :chatWithAI)
                bind("getFileList", :getFileList)
                bind("createNewFile", :createNewFile)
                bind("deleteFile", :deleteFile)
                bind("analyzeCode", :analyzeCode)
                bind("formatCode", :formatCode)
                bind("getCodeSuggestions", :getCodeSuggestions)
                
                # Load the main HTML interface
                setHtml(oUIGenerator.getMainHTML())
                
                # Start the application
                run()
            }
            
        catch
            see "Error starting application: " + cCatchError + nl
        done
    
    # ===================================================================
    # File Operations - Delegate to FileManager
    # ===================================================================
    func saveFile(id, req)
        oFileManager.saveFile(id, req, oWebView)
    
    func loadFile(id, req)
        oFileManager.loadFile(id, req, oWebView)
    
    func createNewFile(id, req)
        oFileManager.createNewFile(id, req, oWebView)
    
    func deleteFile(id, req)
        oFileManager.deleteFile(id, req, oWebView)
    
    func getFileList(id, req)
        oFileManager.getFileList(id, req, oWebView)
    
    # ===================================================================
    # Code Operations - Delegate to CodeRunner
    # ===================================================================
    func runCode(id, req)
        oCodeRunner.runCode(id, req, oWebView)
    
    func formatCode(id, req)
        oCodeRunner.formatCode(id, req, oWebView)
    
    func analyzeCode(id, req)
        oCodeRunner.analyzeCode(id, req, oWebView)
    
    func getCodeSuggestions(id, req)
        oCodeRunner.getCodeSuggestions(id, req, oWebView)
    
    # ===================================================================
    # AI Operations - Delegate to AIAssistant
    # ===================================================================
    func chatWithAI(id, req)
        oAIAssistant.chatWithAI(id, req, oWebView)
