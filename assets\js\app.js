// ===================================================================
// Ring IDE JavaScript Functions
// ===================================================================

// ===================================================================
// Project Management Functions
// ===================================================================
function createProject() {
    const projectName = document.getElementById('projectName').value;
    if (projectName.trim()) {
        updateStatus('تم إنشاء المشروع: ' + projectName);
        updateFileList();
    }
}

function createNewFile() {
    const fileName = prompt('اسم الملف الجديد (مع الامتداد):');
    if (fileName) {
        window.createNewFile(fileName).then(result => {
            if (result) {
                updateFileList();
                updateStatus('تم إنشاء الملف: ' + fileName);
            }
        });
    }
}

// ===================================================================
// File Operations
// ===================================================================
function saveCurrentFile() {
    if (currentFile) {
        const code = editor.getValue();
        window.saveFile(currentFile, code).then(result => {
            if (result) {
                updateStatus('تم حفظ الملف: ' + currentFile);
            } else {
                updateStatus('خطأ في حفظ الملف');
            }
        });
    } else {
        const fileName = prompt('اسم الملف للحفظ:');
        if (fileName) {
            const code = editor.getValue();
            window.saveFile(fileName, code).then(result => {
                if (result) {
                    currentFile = fileName;
                    updateFileList();
                    updateStatus('تم حفظ الملف: ' + fileName);
                }
            });
        }
    }
}

function loadFile(fileName) {
    if (!fileName) {
        fileName = prompt('اسم الملف للفتح:');
    }
    if (fileName) {
        window.loadFile(fileName).then(content => {
            if (content) {
                editor.setValue(content);
                currentFile = fileName;
                updateStatus('تم فتح الملف: ' + fileName);
                highlightActiveFile(fileName);
            } else {
                updateStatus('خطأ في فتح الملف');
            }
        });
    }
}

// ===================================================================
// Code Operations
// ===================================================================
function runCode() {
    const code = editor.getValue();
    updateStatus('جاري تشغيل الكود...');
    window.runCode(code).then(result => {
        addChatMessage('ai', 'نتيجة التشغيل:\n' + result);
        updateStatus('تم تشغيل الكود');
    });
}

function formatCode() {
    const code = editor.getValue();
    window.formatCode(code).then(formattedCode => {
        if (formattedCode) {
            editor.setValue(formattedCode);
            updateStatus('تم تنسيق الكود');
        }
    });
}

function analyzeCode() {
    const code = editor.getValue();
    updateStatus('جاري تحليل الكود...');
    window.analyzeCode(code).then(analysis => {
        addChatMessage('ai', 'تحليل الكود:\n' + analysis);
        updateStatus('تم تحليل الكود');
    });
}

// ===================================================================
// Chat Functions
// ===================================================================
function sendChatMessage() {
    const input = document.getElementById('chatInput');
    const message = input.value.trim();
    if (message) {
        addChatMessage('user', message);
        input.value = '';
        
        showChatLoading(true);
        const currentCode = editor.getValue();
        window.chatWithAI(message, currentCode).then(response => {
            showChatLoading(false);
            addChatMessage('ai', response);
        });
    }
}

function handleChatKeyPress(event) {
    if (event.key === 'Enter') {
        sendChatMessage();
    }
}

function addChatMessage(sender, message) {
    const chatMessages = document.getElementById('chatMessages');
    const messageDiv = document.createElement('div');
    messageDiv.className = 'message ' + sender;
    messageDiv.textContent = message;
    chatMessages.appendChild(messageDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

function showChatLoading(show) {
    const loading = document.getElementById('chatLoading');
    loading.style.display = show ? 'block' : 'none';
}

// ===================================================================
// UI Helper Functions
// ===================================================================
function updateStatus(message) {
    document.getElementById('statusText').textContent = message;
}

function updateFileList() {
    window.getFileList().then(files => {
        const fileList = document.getElementById('fileList');
        fileList.innerHTML = '';
        
        if (files && files.length > 0) {
            files.forEach(file => {
                const fileDiv = document.createElement('div');
                fileDiv.className = 'file-item';
                fileDiv.textContent = file;
                fileDiv.onclick = () => loadFile(file);
                fileList.appendChild(fileDiv);
            });
        } else {
            fileList.innerHTML = '<div style="color: #7f8c8d; text-align: center; padding: 10px;">لا توجد ملفات</div>';
        }
    });
}

function highlightActiveFile(fileName) {
    const fileItems = document.querySelectorAll('.file-item');
    fileItems.forEach(item => {
        item.classList.remove('active');
        if (item.textContent === fileName) {
            item.classList.add('active');
        }
    });
}

// ===================================================================
// Initialize Application
// ===================================================================
document.addEventListener('DOMContentLoaded', function() {
    updateStatus('Ring Programming IDE جاهز للاستخدام');
    updateFileList();
});
